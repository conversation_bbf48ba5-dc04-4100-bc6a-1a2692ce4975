
import React, { useState, useEffect } from 'react';
import { MapP<PERSON>, Clock } from 'lucide-react';

const LiveActivity = () => {
  const [currentActivity, setCurrentActivity] = useState(0);
  const [spotsRemaining, setSpotsRemaining] = useState(4);

  const activities = [
    { name: "<PERSON> from Austin", action: "booked his demo", time: "2 mins ago", location: "Austin, TX" },
    { name: "<PERSON> from Phoenix", action: "scheduled consultation", time: "5 mins ago", location: "Phoenix, AZ" },
    { name: "<PERSON> from Denver", action: "started onboarding", time: "8 mins ago", location: "Denver, CO" },
    { name: "<PERSON> from Miami", action: "booked his demo", time: "12 mins ago", location: "Miami, FL" },
    { name: "<PERSON> from Seattle", action: "activated AI system", time: "15 mins ago", location: "Seattle, WA" }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentActivity((prev) => (prev + 1) % activities.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [activities.length]);

  useEffect(() => {
    // Update spots based on current date logic
    const today = new Date();
    const dayOfMonth = today.getDate();
    const remaining = Math.max(1, 7 - Math.floor((dayOfMonth - 1) / 4));
    setSpotsRemaining(remaining);
  }, []);

  return (
    <div className="fixed top-24 right-4 md:top-6 md:right-6 z-40 space-y-4 max-w-xs md:max-w-sm hidden sm:block">
      {/* Live Activity Feed */}
      <div className="bg-gradient-to-r from-gray-900/95 via-gray-800/95 to-gray-900/95 backdrop-blur-3xl rounded-2xl p-3 md:p-4 border border-gray-600/40 shadow-2xl shadow-black/50 animate-fade-in">
        <div className="flex items-start space-x-2 md:space-x-3">
          <div className="w-2 h-2 md:w-3 md:h-3 bg-green-500 rounded-full animate-pulse mt-2 shadow-lg shadow-green-500/60"></div>
          <div>
            <div className="text-white text-xs md:text-sm font-bold">
              📍 {activities[currentActivity].name} just {activities[currentActivity].action}
            </div>
            <div className="text-gray-400 text-xs flex items-center mt-1">
              <Clock className="w-2 h-2 md:w-3 md:h-3 mr-1" />
              {activities[currentActivity].time}
            </div>
          </div>
        </div>
      </div>

      {/* Scarcity Badge */}
      <div className="bg-gradient-to-r from-red-600/95 via-red-500/95 to-red-600/95 backdrop-blur-3xl rounded-2xl p-3 md:p-4 border border-red-500/40 shadow-2xl shadow-red-600/50">
        <div className="text-center">
          <div className="text-white text-base md:text-lg font-black">
            {spotsRemaining}/7 Spots Remaining
          </div>
          <div className="text-red-200 text-xs md:text-sm font-medium">
            This Month
          </div>
        </div>
      </div>

      {/* Pipeline Badge */}
      <div className="bg-gradient-to-r from-blue-600/95 via-blue-500/95 to-blue-600/95 backdrop-blur-3xl rounded-2xl p-3 md:p-4 border border-blue-500/40 shadow-2xl shadow-blue-600/50">
        <div className="text-center">
          <div className="text-white text-base md:text-lg font-black">
            $2.3M+
          </div>
          <div className="text-blue-200 text-xs md:text-sm font-medium">
            Pipeline Booked in 2025
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveActivity;
