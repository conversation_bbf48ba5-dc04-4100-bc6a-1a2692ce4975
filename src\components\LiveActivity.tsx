
import React, { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

const LiveActivity = () => {
  const [currentActivity, setCurrentActivity] = useState(0);
  const [spotsRemaining, setSpotsRemaining] = useState(4);

  const activities = [
    { name: "<PERSON> from Austin", action: "booked his demo", time: "2 mins ago", location: "Austin, TX" },
    { name: "<PERSON> from Phoenix", action: "scheduled consultation", time: "5 mins ago", location: "Phoenix, AZ" },
    { name: "<PERSON> from Denver", action: "started onboarding", time: "8 mins ago", location: "Denver, CO" },
    { name: "<PERSON> from Miami", action: "booked his demo", time: "12 mins ago", location: "Miami, FL" },
    { name: "<PERSON> from Seattle", action: "activated AI system", time: "15 mins ago", location: "Seattle, WA" }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentActivity((prev) => (prev + 1) % activities.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [activities.length]);

  useEffect(() => {
    // Update spots based on current date logic
    const today = new Date();
    const dayOfMonth = today.getDate();
    const remaining = Math.max(1, 7 - Math.floor((dayOfMonth - 1) / 4));
    setSpotsRemaining(remaining);
  }, []);

  // Show activity toast notifications
  useEffect(() => {
    const showActivityToast = () => {
      const activity = activities[currentActivity];
      toast({
        title: (
          <div className="flex items-start space-x-3 w-full">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse mt-1 shadow-lg shadow-green-500/60"></div>
            <div className="flex-1">
              <div className="text-white text-sm font-bold">
                📍 {activity.name} just {activity.action}
              </div>
              <div className="flex items-center text-gray-400 text-xs mt-1">
                <Clock className="w-3 h-3 mr-1" />
                {activity.time}
              </div>
            </div>
          </div>
        ),
        className: "bg-gradient-to-r from-gray-900/95 via-gray-800/95 to-gray-900/95 backdrop-blur-3xl border-gray-600/40 shadow-2xl shadow-black/50 max-w-sm",
        duration: 3500,
      });
    };

    // Show initial toast after a short delay
    const initialTimeout = setTimeout(showActivityToast, 1000);

    // Then show subsequent toasts with the interval
    const interval = setInterval(showActivityToast, 4000);

    return () => {
      clearTimeout(initialTimeout);
      clearInterval(interval);
    };
  }, [currentActivity, activities]);

  // Show scarcity toast periodically
  useEffect(() => {
    const showScarcityToast = () => {
      toast({
        title: (
          <div className="text-center w-full py-1">
            <div className="text-white text-lg font-black">
              {spotsRemaining}/7 Spots Remaining
            </div>
            <div className="text-red-200 text-sm font-medium">
              This Month
            </div>
          </div>
        ),
        className: "bg-gradient-to-r from-red-600/95 via-red-500/95 to-red-600/95 backdrop-blur-3xl border-red-500/40 shadow-2xl shadow-red-600/50 max-w-sm",
        duration: 4000,
      });
    };

    // Show scarcity toast every 15 seconds, starting after 5 seconds
    const initialTimeout = setTimeout(showScarcityToast, 5000);
    const interval = setInterval(showScarcityToast, 15000);

    return () => {
      clearTimeout(initialTimeout);
      clearInterval(interval);
    };
  }, [spotsRemaining]);

  // Show pipeline toast periodically
  useEffect(() => {
    const showPipelineToast = () => {
      toast({
        title: (
          <div className="text-center w-full py-1">
            <div className="text-white text-lg font-black">
              $2.3M+
            </div>
            <div className="text-blue-200 text-sm font-medium">
              Pipeline Booked in 2025
            </div>
          </div>
        ),
        className: "bg-gradient-to-r from-blue-600/95 via-blue-500/95 to-blue-600/95 backdrop-blur-3xl border-blue-500/40 shadow-2xl shadow-blue-600/50 max-w-sm",
        duration: 4000,
      });
    };

    // Show pipeline toast every 20 seconds, starting after 10 seconds
    const initialTimeout = setTimeout(showPipelineToast, 10000);
    const interval = setInterval(showPipelineToast, 20000);

    return () => {
      clearTimeout(initialTimeout);
      clearInterval(interval);
    };
  }, []);

  // This component no longer renders any visible UI - it just manages toast notifications
  return null;
};

export default LiveActivity;
