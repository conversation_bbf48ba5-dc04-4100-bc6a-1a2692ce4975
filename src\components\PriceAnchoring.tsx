
import React from 'react';
import { DollarSign, Users, Zap } from 'lucide-react';

const PriceAnchoring = () => {
  return (
    <section className="py-32 bg-gradient-to-b from-black via-gray-900/30 to-black relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-1/4 w-[600px] h-[600px] bg-green-600/10 rounded-full blur-[120px]"></div>
        <div className="absolute bottom-1/4 left-1/4 w-[500px] h-[500px] bg-green-600/8 rounded-full blur-[100px]"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-6xl mx-auto text-center">
          <div className="space-y-16">
            <h2 className="text-6xl lg:text-8xl font-black text-white leading-tight tracking-tight">
              Here's What This <span className="text-transparent bg-gradient-to-r from-green-500 via-green-400 to-green-600 bg-clip-text">Really Costs</span>
            </h2>

            <div className="grid md:grid-cols-3 gap-12">
              <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 rounded-3xl p-12 border border-gray-600/40 hover:border-red-600/40 transition-all duration-500">
                <Zap className="w-16 h-16 text-red-400 mx-auto mb-6" />
                <div className="text-3xl font-black text-white mb-4">AI Caller Alone</div>
                <div className="text-5xl font-black text-red-400 mb-4">$9,800</div>
                <div className="text-gray-300 text-lg">What we used to charge just for the AI voice system</div>
              </div>

              <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 rounded-3xl p-12 border border-gray-600/40 hover:border-blue-600/40 transition-all duration-500">
                <Users className="w-16 h-16 text-blue-400 mx-auto mb-6" />
                <div className="text-3xl font-black text-white mb-4">Part-Time Receptionist</div>
                <div className="text-5xl font-black text-blue-400 mb-4">$2,400</div>
                <div className="text-gray-300 text-lg">Per month for someone who might miss calls</div>
              </div>

              <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 rounded-3xl p-12 border border-gray-600/40 hover:border-green-600/40 transition-all duration-500">
                <DollarSign className="w-16 h-16 text-green-400 mx-auto mb-6" />
                <div className="text-3xl font-black text-white mb-4">Lost Revenue</div>
                <div className="text-5xl font-black text-green-400 mb-4">$50K+</div>
                <div className="text-gray-300 text-lg">What you lose annually from slow follow-up</div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-600/30 via-green-500/20 to-green-700/30 border-2 border-green-600/50 rounded-[3rem] p-16 backdrop-blur-sm shadow-2xl shadow-green-600/30">
              <div className="space-y-8">
                <h3 className="text-5xl font-black text-white leading-tight">
                  We Bundle Everything for <span className="text-green-300">Less Than a Part-Timer</span>
                </h3>
                <div className="grid md:grid-cols-2 gap-8 text-left">
                  <div className="space-y-4">
                    <div className="text-green-200 text-xl font-bold">✅ Custom AI Assistant ($9,800 value)</div>
                    <div className="text-green-200 text-xl font-bold">✅ 3 Months Done-For-You Ads ($9,800 value)</div>
                    <div className="text-green-200 text-xl font-bold">✅ Complete Training & SOPs ($2,500 value)</div>
                  </div>
                  <div className="space-y-4">
                    <div className="text-green-200 text-xl font-bold">✅ Full System Ownership (Priceless)</div>
                    <div className="text-green-200 text-xl font-bold">✅ 15-Appointment Guarantee</div>
                    <div className="text-green-200 text-xl font-bold">✅ 24/7 Never-Miss-a-Lead Coverage</div>
                  </div>
                </div>
                <div className="text-center pt-8 border-t border-green-600/30">
                  <div className="text-green-200 text-2xl font-bold mb-4">
                    This system prevents the $50K+ annual loss from missed opportunities
                  </div>
                  <div className="text-3xl font-black text-white">
                    Your investment? A fraction of what you'd pay a part-time receptionist.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PriceAnchoring;
