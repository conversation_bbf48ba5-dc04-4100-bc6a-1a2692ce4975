
import React from 'react';

const MetricsSection = () => {
  const metrics = [
    {
      number: "92%",
      label: "Faster Response Time",
      description: "vs. manual follow-up"
    },
    {
      number: "15+",
      label: "Guaranteed Appointments",
      description: "in first 30 days"
    },
    {
      number: "$150K+",
      label: "Pipeline Booked",
      description: "average first month"
    },
    {
      number: "<2 Min",
      label: "Average Response Time",
      description: "24/7 availability"
    }
  ];

  return (
    <section className="py-20 md:py-32 lg:py-40 bg-gradient-to-b from-black via-gray-900/30 to-black relative overflow-hidden">
      {/* Enhanced background effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-1/4 w-[800px] h-[800px] bg-red-600/15 rounded-full blur-[150px]"></div>
        <div className="absolute bottom-1/4 left-1/4 w-[700px] h-[700px] bg-red-600/10 rounded-full blur-[130px]"></div>
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.015)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.015)_1px,transparent_1px)] bg-[size:160px_160px]"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="max-w-8xl mx-auto">
          <div className="text-center mb-16 md:mb-24 lg:mb-32">
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl 2xl:text-9xl font-black text-white mb-8 md:mb-12 leading-[0.85] tracking-tighter">
              Results That Speak for <span className="text-transparent bg-gradient-to-r from-red-500 via-red-400 to-red-600 bg-clip-text">Themselves</span>
            </h2>
            <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl text-gray-300 font-bold">
              Real numbers from real roofing contractors
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 lg:gap-10">
            {metrics.map((metric, index) => (
              <div key={index} className="group hover:scale-105 transition-all duration-700">
                <div className="bg-gradient-to-br from-gray-900/95 via-gray-800/80 to-gray-900/95 backdrop-blur-3xl rounded-[1.5rem] md:rounded-[2rem] lg:rounded-[2.5rem] p-6 md:p-8 lg:p-12 text-center border border-gray-600/40 hover:border-red-600/60 transition-all duration-700 shadow-[0_40px_80px_-20px_rgba(0,0,0,0.9)] group-hover:shadow-[0_50px_100px_-20px_rgba(220,38,38,0.4)] relative overflow-hidden h-full flex flex-col justify-center min-h-[280px] md:min-h-[320px] lg:min-h-[360px]">

                  {/* Gradient overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-red-600/15 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 rounded-[1.5rem] md:rounded-[2rem] lg:rounded-[2.5rem]"></div>

                  <div className="relative z-10 space-y-4 md:space-y-6">
                    <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-transparent bg-gradient-to-r from-red-500 via-red-400 to-red-600 bg-clip-text group-hover:scale-110 transition-transform duration-700">
                      {metric.number}
                    </div>
                    <div className="text-base md:text-lg lg:text-xl xl:text-2xl font-black text-white">
                      {metric.label}
                    </div>
                    <div className="text-gray-300 text-sm md:text-base lg:text-lg font-medium">
                      {metric.description}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default MetricsSection;
