
import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

const FAQSection = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: "I've been burned by marketing agencies before. What makes you different?",
      answer: "We hear you. We're not an agency that rents you leads; we are an implementation partner that builds you a permanent sales asset. The system combines proprietary AI follow-up tech, the expertise of former Facebook insiders, and hands-on training. Plus, our guarantee means you don't pay until you see results. We're built for long-term independence, not endless retainers."
    },
    {
      question: "Will this work in my area/for my type of roofing business?",
      answer: "Absolutely. The Growth Engine is customized for every client. Our ad specialists perform deep research on your local market to craft campaigns for your specific services, whether it's residential re-roofs or storm restoration. On our call, we'll map out a plan tailored to your exact area and discuss specific results we can achieve."
    },
    {
      question: "Does the AI sound human? Will prospects trust an AI caller?",
      answer: "In testing, prospects often couldn't tell the difference. Our AI uses advanced natural language technology and sounds completely human. If a prospect ever asks, the AI can seamlessly transfer to a human team member. The key is that it responds instantly – something your competitors can't match."
    },
    {
      question: "How much time will this take me? I'm already swamped.",
      answer: "This system is designed to save you time, not add to your workload. We handle all the heavy lifting: the AI manages lead follow-up and scheduling, and our team runs the ads. Your only involvement is a brief weekly check-in and optional training sessions. It's like adding a virtual sales assistant to your team."
    },
    {
      question: "What if I don't see results in 3 months?",
      answer: "We guarantee 5 qualified appointments in your first 30 days, or you receive a 100% refund. Most clients exceed this significantly and see continued benefits long-term. If you don't see results in 3 months, we'll work with you at no extra cost until you do."
    },
    {
      question: "Is this complicated to set up or use?",
      answer: "Not at all. We handle the entire setup for you – the AI assistant, ad campaigns, and integrations. If you can check email, you can use our system. We provide full training and support, and most clients are seeing appointments within the first week."
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center space-y-6 mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white">
              Your Questions <span className="text-red-600">Answered</span>
            </h2>
            <p className="text-xl text-gray-400">
              Everything you need to know about the Roofers Growth Engine
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden">
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-gray-800/50 transition-colors"
                >
                  <h3 className="text-xl font-bold text-white pr-4">{faq.question}</h3>
                  {openIndex === index ? (
                    <ChevronUp className="w-6 h-6 text-red-600 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-6 h-6 text-red-600 flex-shrink-0" />
                  )}
                </button>

                {openIndex === index && (
                  <div className="px-6 pb-6">
                    <p className="text-gray-400 text-lg leading-relaxed">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
