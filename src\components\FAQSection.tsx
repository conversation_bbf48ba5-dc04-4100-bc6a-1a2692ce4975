
import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

const FAQSection = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: "I've been burned by marketing agencies before. What makes you different?",
      answer: "We hear you. We're not an agency that rents you leads; we are an implementation partner that builds you a permanent sales asset. The system combines proprietary AI follow-up tech, the expertise of former Facebook insiders, and hands-on training. Plus, our guarantee means you don't pay until you see results. We're built for long-term independence, not endless retainers."
    },
    {
      question: "Will this work for my specific roofing business in my local area?",
      answer: "Absolutely. The Growth Engine is customized for every client. Our ad specialists perform deep research on your local market to craft campaigns for your specific services, whether it's residential re-roofs or storm restoration. On our call, we'll map out a plan tailored to your exact area."
    },
    {
      question: "How much time does this take? I'm already swamped.",
      answer: "This system is designed to save you time. We handle all the heavy lifting: the AI manages lead follow-up and scheduling, and our team runs the ads. Your only involvement is a brief weekly check-in. It's like adding a virtual sales assistant to your team, not more work to your plate."
    },
    {
      question: "How does your AI agent work?",
      answer: "Our AI uses advanced voice technology trained specifically for roofing conversations. It can handle objections, qualify prospects, and book appointments using natural conversation. The AI is programmed with proven roofing sales scripts and can adapt to different scenarios in real-time."
    },
    {
      question: "Do I need to run Facebook ads?",
      answer: "Not necessarily. The system works with any lead source - Facebook ads, Google ads, referrals, or existing leads. However, our managed Facebook ad service provides the most predictable and scalable results for most roofing contractors."
    },
    {
      question: "What if I don't get 15 appointments in the first month?",
      answer: "We guarantee 5 qualified appointments in your first 30 days, or you receive a 100% refund. Most clients exceed this significantly, but we stand behind our promise with this ironclad guarantee."
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center space-y-6 mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white">
              Your Questions <span className="text-red-600">Answered</span>
            </h2>
            <p className="text-xl text-gray-400">
              Everything you need to know about the Roofers Growth Engine
            </p>
          </div>
          
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden">
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-gray-800/50 transition-colors"
                >
                  <h3 className="text-xl font-bold text-white pr-4">{faq.question}</h3>
                  {openIndex === index ? (
                    <ChevronUp className="w-6 h-6 text-red-600 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-6 h-6 text-red-600 flex-shrink-0" />
                  )}
                </button>
                
                {openIndex === index && (
                  <div className="px-6 pb-6">
                    <p className="text-gray-400 text-lg leading-relaxed">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
