
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Calendar } from 'lucide-react';

const StickyMobileCTA = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // Show after scrolling past hero section
      setIsVisible(window.scrollY > 800);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-60 md:hidden">
      <div className="bg-gradient-to-r from-red-600 via-red-500 to-red-600 backdrop-blur-3xl border-t border-red-500/40 shadow-2xl shadow-red-600/50 p-3 md:p-4">
        <Button
          onClick={() => scrollToSection('final-cta')}
          className="w-full bg-white text-red-600 hover:bg-gray-100 py-3 md:py-4 text-base md:text-lg font-black rounded-xl md:rounded-2xl transition-all duration-300 shadow-xl"
        >
          <Calendar className="w-4 h-4 md:w-5 md:h-5 mr-2" />
          📅 Book My Free Strategy Call
        </Button>
        <p className="text-red-100 text-xs text-center mt-2 font-medium">
          Free consultation • 5 appointments guaranteed or money back
        </p>
      </div>
    </div>
  );
};

export default StickyMobileCTA;
