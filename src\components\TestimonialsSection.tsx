
import React from 'react';

const TestimonialsSection = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      company: "Phoenix Roofing Pros",
      location: "Phoenix, AZ",
      rating: 5,
      quote: "In just 2 months, the AI and new ad strategy tripled our weekly appointments. We went from struggling to fill our schedule to being booked out 3 weeks in advance. It's been a complete game-changer.",
      result: "$50k+ in new projects"
    },
    {
      name: "<PERSON>",
      company: "Storm Shield Roofing",
      location: "Houston, TX", 
      rating: 5,
      quote: "The AI never misses a call and books appointments while I'm sleeping. Last month we closed $78K in new business, and I barely had to touch the phone. This system pays for itself.",
      result: "300% ROI in 60 days"
    },
    {
      name: "<PERSON>",
      company: "Elite Roof Solutions",
      location: "Denver, CO",
      rating: 5,
      quote: "Finally, a marketing system that actually works. The AI is incredibly natural - prospects don't even realize they're talking to a bot. We've gone from 3-4 appointments per week to 15+.",
      result: "5x appointment volume"
    }
  ];

  return (
    <section id="case-studies" className="py-48 bg-gradient-to-b from-gray-900/50 via-black to-gray-900/30 relative overflow-hidden">
      {/* Enhanced background effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/3 left-1/3 w-[800px] h-[800px] bg-red-600/15 rounded-full blur-[150px]"></div>
        <div className="absolute bottom-1/3 right-1/3 w-[700px] h-[700px] bg-red-600/10 rounded-full blur-[130px]"></div>
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.015)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.015)_1px,transparent_1px)] bg-[size:140px_140px]"></div>
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-8xl mx-auto">
          <div className="text-center space-y-12 mb-32">
            <h2 className="text-7xl lg:text-9xl font-black text-white leading-[0.85] tracking-tighter">
              Real Results from <span className="text-transparent bg-gradient-to-r from-red-500 via-red-400 to-red-600 bg-clip-text">Real Roofers</span>
            </h2>
            <p className="text-4xl text-gray-300 font-bold">
              Don't just take our word for it - see what our clients are saying
            </p>
          </div>
          
          <div className="grid lg:grid-cols-3 gap-16">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="group hover:scale-105 transition-all duration-700">
                <div className="bg-gradient-to-br from-gray-900/95 via-gray-800/80 to-gray-900/95 backdrop-blur-3xl rounded-[2.5rem] p-16 border border-gray-600/40 hover:border-red-600/50 transition-all duration-700 shadow-[0_40px_80px_-20px_rgba(0,0,0,0.9)] h-full relative overflow-hidden group-hover:shadow-[0_50px_100px_-20px_rgba(220,38,38,0.3)]">
                  
                  {/* Gradient overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-red-600/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 rounded-[2.5rem]"></div>
                  
                  <div className="relative z-10">
                    <div className="flex items-center justify-between mb-12">
                      <div className="flex text-yellow-400 space-x-2">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <span key={i} className="text-3xl">★</span>
                        ))}
                      </div>
                      <div className="bg-gradient-to-r from-red-600 to-red-700 text-white px-8 py-4 rounded-2xl text-lg font-black shadow-2xl shadow-red-600/50">
                        {testimonial.result}
                      </div>
                    </div>
                    
                    <blockquote className="text-gray-200 text-2xl mb-12 italic leading-relaxed font-semibold">
                      "{testimonial.quote}"
                    </blockquote>
                    
                    <div className="border-t border-gray-600/50 pt-10">
                      <div className="font-black text-white text-3xl">{testimonial.name}</div>
                      <div className="text-gray-300 text-xl mt-2 font-semibold">{testimonial.company}</div>
                      <div className="text-gray-400 mt-1 text-lg">{testimonial.location}</div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
