
import React from 'react';
import Navigation from '@/components/Navigation';
import HeroSection from '@/components/HeroSection';
import LiveActivity from '@/components/LiveActivity';
import CredibilityBar from '@/components/CredibilityBar';
import InteractiveDemo from '@/components/InteractiveDemo';
import PainSection from '@/components/PainSection';
import SolutionSection from '@/components/SolutionSection';
import HowItWorks from '@/components/HowItWorks';
import MetricsSection from '@/components/MetricsSection';
import SoftOptIn from '@/components/SoftOptIn';
import CaseStudyTestimonials from '@/components/CaseStudyTestimonials';
import PriceAnchoring from '@/components/PriceAnchoring';
import OfferStack from '@/components/OfferStack';
import FAQSection from '@/components/FAQSection';
import FinalCTA from '@/components/FinalCTA';
import Footer from '@/components/Footer';
import StickyMobileCTA from '@/components/StickyMobileCTA';

const Index = () => {
  return (
    <div className="min-h-screen bg-black">
      <Navigation />
      <LiveActivity />
      <HeroSection />
      <CredibilityBar />
      <InteractiveDemo />
      <PainSection />
      <SolutionSection />
      <HowItWorks />
      <MetricsSection />
      <SoftOptIn />
      <CaseStudyTestimonials />
      <PriceAnchoring />
      <OfferStack />
      <FAQSection />
      <FinalCTA />
      <Footer />
      <StickyMobileCTA />
    </div>
  );
};

export default Index;
