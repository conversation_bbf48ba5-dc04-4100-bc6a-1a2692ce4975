import React from 'react';
import { Button } from '@/components/ui/button';
import { Play } from 'lucide-react';

const HeroSection = () => {
  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="min-h-screen bg-gradient-to-br from-black via-gray-900/50 to-black flex items-center relative overflow-hidden">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-red-600/10 via-transparent to-red-600/10"></div>
        <div className="absolute top-1/4 left-1/4 w-[800px] h-[800px] bg-red-600/30 rounded-full blur-[150px] animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-[600px] h-[600px] bg-red-600/15 rounded-full blur-[120px]"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[1200px] h-[1200px] bg-gradient-radial from-red-600/5 to-transparent rounded-full"></div>

        {/* Enhanced Grid overlay */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:80px_80px]"></div>

        {/* Additional floating elements */}
        <div className="absolute top-20 right-20 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
        <div className="absolute bottom-40 left-20 w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
        <div className="absolute top-60 left-1/3 w-1 h-1 bg-red-300 rounded-full animate-pulse"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 py-16 md:py-20 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
          <div className="space-y-12 md:space-y-16">
            <div className="space-y-8 md:space-y-10">
              <div className="inline-flex items-center px-4 md:px-6 py-2 md:py-3 bg-gradient-to-r from-red-600/20 via-red-500/15 to-red-600/20 border border-red-600/30 rounded-full text-red-300 text-sm md:text-base font-semibold backdrop-blur-sm shadow-lg shadow-red-600/20">
                <div className="w-2 h-2 md:w-3 md:h-3 bg-red-500 rounded-full mr-2 md:mr-3 animate-pulse shadow-lg shadow-red-500/50"></div>
                For Residential Roofing Contractors
              </div>

              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-black text-white leading-[0.85] tracking-tighter">
                Consistent Roofing Leads
                <span className="block mt-2 md:mt-4 lg:mt-6 text-transparent bg-gradient-to-r from-red-500 via-red-400 to-red-600 bg-clip-text">
                  on Autopilot
                </span>
                <span className="block mt-2 md:mt-4 lg:mt-6 text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl">– Powered by AI.</span>
              </h1>

              <div className="space-y-6 md:space-y-8">
                <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl text-gray-100 font-bold leading-tight">
                  An all-in-one AI sales assistant + ads system that books appointments for your roofing business, even while you sleep.
                </p>
                <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-300 max-w-2xl leading-relaxed font-medium">
                  Never waste leads or ad spend again. Our AI calls every prospect within 120 seconds and books qualified appointments directly to your calendar.
                </p>
              </div>
            </div>

            <div className="space-y-6 md:space-y-8">
              <div className="space-y-3 md:space-y-4">
                <Button
                  onClick={() => scrollToSection('final-cta')}
                  size="lg"
                  className="w-full cta-primary cta-pulse text-white px-8 md:px-12 lg:px-16 py-6 md:py-8 lg:py-10 text-lg md:text-xl lg:text-2xl xl:text-3xl font-black rounded-xl md:rounded-2xl lg:rounded-3xl border-0 group attention-grabber"
                >
                  <span className="relative z-10 flex items-center justify-center gap-3">
                    <span className="text-2xl md:text-3xl">📅</span>
                    <span>Book My Free Strategy Call</span>
                    <span className="text-xl md:text-2xl animate-bounce">→</span>
                  </span>
                </Button>
                <p className="text-gray-400 text-sm md:text-base text-center">
                  Free 30-minute consultation – no obligation<br />
                  <span className="text-red-300 font-semibold">Get a custom marketing gameplan for your roofing business</span>
                </p>
              </div>

              <div className="bg-gradient-to-r from-green-600/20 via-green-500/15 to-green-600/20 border-2 border-green-600/40 rounded-2xl p-6 md:p-8 backdrop-blur-sm card-hover attention-grabber">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <span className="text-3xl animate-pulse">🛡️</span>
                  <span className="text-green-300 font-black text-xl md:text-2xl">Our Guarantee</span>
                </div>
                <p className="text-gray-100 text-center text-base md:text-lg font-semibold leading-relaxed">
                  <span className="text-white font-black text-lg md:text-xl">5 qualified appointments</span> in 30 days or <span className="text-green-300 font-black text-lg md:text-xl">100% money back</span>
                </p>
              </div>
            </div>
          </div>

          <div className="relative mt-12 lg:mt-0 mx-4 md:mx-8 lg:mx-12">
            {/* Enhanced floating card with better gradients and spacing */}
            <div className="relative z-30 bg-gradient-to-br from-gray-800/95 via-gray-900/90 to-black/95 backdrop-blur-3xl rounded-[2rem] md:rounded-[3rem] p-6 md:p-12 shadow-[0_30px_60px_-20px_rgba(0,0,0,0.9)] md:shadow-[0_50px_100px_-20px_rgba(0,0,0,0.9)] border border-gray-600/40 hover:border-red-600/40 transition-all duration-700 hover:shadow-[0_60px_120px_-20px_rgba(220,38,38,0.3)]">
              <div className="bg-gradient-to-br from-gray-900/98 to-black/98 rounded-[1.5rem] md:rounded-[2.5rem] p-6 md:p-10 space-y-6 md:space-y-10">
                <div className="flex items-center space-x-3 md:space-x-5">
                  <div className="w-4 h-4 md:w-6 md:h-6 bg-green-500 rounded-full animate-pulse shadow-xl shadow-green-500/60 ring-4 md:ring-8 ring-green-500/20"></div>
                  <span className="text-green-300 text-lg md:text-2xl font-black">AI Agent Active</span>
                </div>

                <div className="space-y-4 md:space-y-8">
                  <div className="bg-gradient-to-r from-red-600/30 via-red-500/20 to-red-600/30 border-2 border-red-600/50 rounded-2xl md:rounded-3xl p-4 md:p-8 backdrop-blur-sm transform hover:scale-105 transition-all duration-500 shadow-lg shadow-red-600/20">
                    <div className="text-red-300 text-sm md:text-base font-black mb-2 md:mb-4">🔔 New Lead Alert</div>
                    <div className="text-white text-lg md:text-2xl font-bold">Homeowner in Phoenix - Roof Replacement</div>
                    <div className="text-gray-300 text-base md:text-lg mt-2 md:mt-3">$12,500 project value</div>
                  </div>

                  <div className="bg-gradient-to-r from-blue-600/30 via-blue-500/20 to-blue-600/30 border-2 border-blue-600/50 rounded-2xl md:rounded-3xl p-4 md:p-8 backdrop-blur-sm transform hover:scale-105 transition-all duration-500 shadow-lg shadow-blue-600/20">
                    <div className="text-blue-300 text-sm md:text-base font-black mb-2 md:mb-4">📞 AI Calling...</div>
                    <div className="text-white text-lg md:text-2xl font-bold">Response time: 47 seconds</div>
                    <div className="text-gray-300 text-base md:text-lg mt-2 md:mt-3">Qualifying prospect now</div>
                  </div>

                  <div className="bg-gradient-to-r from-green-600/30 via-green-500/20 to-green-600/30 border-2 border-green-600/50 rounded-2xl md:rounded-3xl p-4 md:p-8 backdrop-blur-sm transform hover:scale-105 transition-all duration-500 shadow-lg shadow-green-600/20">
                    <div className="text-green-300 text-sm md:text-base font-black mb-2 md:mb-4">✅ Appointment Booked</div>
                    <div className="text-white text-lg md:text-2xl font-bold">Tuesday at 2:30 PM - $15K Project</div>
                    <div className="text-gray-300 text-base md:text-lg mt-2 md:mt-3">High-intent prospect confirmed</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Floating Elements with better positioning to prevent overlap */}
            <div className="absolute -top-4 -right-4 lg:-top-8 lg:-right-8 xl:-top-12 xl:-right-12 bg-gradient-to-r from-red-600 via-red-500 to-red-700 text-white px-4 py-2 lg:px-6 lg:py-3 xl:px-8 xl:py-4 rounded-xl lg:rounded-2xl xl:rounded-3xl text-sm lg:text-lg xl:text-xl font-black animate-bounce shadow-2xl shadow-red-600/50 border-2 border-red-400/30 hidden md:block z-20">
              +$50K This Month
            </div>
            <div className="absolute -bottom-4 -left-4 lg:-bottom-8 lg:-left-8 xl:-bottom-12 xl:-left-12 bg-gradient-to-r from-green-600 via-green-500 to-green-700 text-white px-4 py-2 lg:px-6 lg:py-3 xl:px-8 xl:py-4 rounded-xl lg:rounded-2xl xl:rounded-3xl text-sm lg:text-lg xl:text-xl font-black shadow-2xl shadow-green-600/50 border-2 border-green-400/30 hidden md:block z-20">
              92% Response Rate
            </div>

            {/* Enhanced glow effects */}
            <div className="absolute inset-0 bg-gradient-to-r from-red-600/20 to-green-600/20 rounded-[3rem] blur-3xl -z-10"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-red-600/10 via-blue-600/10 to-green-600/10 rounded-[3rem] blur-2xl -z-20"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
