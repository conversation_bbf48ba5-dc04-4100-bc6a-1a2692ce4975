
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

const Navigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-700 ${
        isScrolled
          ? 'bg-black/98 backdrop-blur-3xl border-b border-gray-700/60 shadow-2xl shadow-black/40'
          : 'bg-transparent'
      }`}
    >
      <div className="container mx-auto px-4 md:px-6 py-6 md:py-8">
        <div className="flex items-center justify-between">
          <div className="text-white font-black text-xl md:text-3xl tracking-tight">
            Roofers Growth Engine
          </div>

          <div className="hidden md:flex items-center space-x-8 lg:space-x-16">
            <button
              onClick={() => scrollToSection('how-it-works')}
              className="text-gray-200 hover:text-white transition-all duration-300 text-lg lg:text-xl font-bold hover:scale-105"
            >
              How It Works
            </button>
            <button
              onClick={() => scrollToSection('case-studies')}
              className="text-gray-200 hover:text-white transition-all duration-300 text-lg lg:text-xl font-bold hover:scale-105"
            >
              Case Studies
            </button>
            <button
              onClick={() => scrollToSection('pricing')}
              className="text-gray-200 hover:text-white transition-all duration-300 text-lg lg:text-xl font-bold hover:scale-105"
            >
              Pricing
            </button>

            <Button
              onClick={() => scrollToSection('final-cta')}
              className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 lg:px-10 py-3 lg:py-4 text-lg lg:text-xl font-black rounded-3xl transition-all duration-300 shadow-2xl shadow-red-600/40 hover:shadow-red-600/60 hover:scale-105 border-0"
            >
              Book Demo
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              onClick={() => scrollToSection('final-cta')}
              className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-4 py-2 text-sm font-black rounded-2xl transition-all duration-300 shadow-xl shadow-red-600/40 hover:shadow-red-600/60 border-0"
            >
              Book Demo
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
