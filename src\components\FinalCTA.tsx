
import React from 'react';
import { Button } from '@/components/ui/button';
import { Clock } from 'lucide-react';

const FinalCTA = () => {
  return (
    <section id="final-cta" className="py-16 md:py-20 lg:py-24 bg-gradient-to-t from-red-900/20 via-black to-black">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-4xl mx-auto text-center space-y-8 md:space-y-12">
          <div className="space-y-4 md:space-y-6">
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white">
              Ready to Stop Relying on <span className="text-red-600">Luck?</span>
            </h2>
            <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto">
              The Roofers Growth Engine gives you the team, technology, and training to win new jobs every single month. Don't let another high-value lead slip through the cracks.
            </p>
          </div>

          <div className="bg-red-600/10 border border-red-600/30 rounded-xl p-6 mb-8">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <Clock className="w-6 h-6 text-red-400" />
              <span className="text-red-400 font-bold text-lg">Limited Availability</span>
            </div>
            <p className="text-gray-300">
              To ensure quality, we only onboard <span className="text-red-400 font-bold">5 new roofing companies per month</span>. Our next slots are nearly full.
            </p>
          </div>

          <div className="space-y-6">
            <Button
              size="lg"
              className="bg-red-600 hover:bg-red-700 text-white px-12 py-6 text-2xl rounded-xl transition-all duration-300 shadow-2xl hover:shadow-red-600/25 hover:scale-105"
            >
              Book My Free Strategy Call Now
            </Button>

            <div className="bg-gray-900/50 rounded-xl p-6 border border-gray-800">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <span className="text-2xl">🛡️</span>
                <span className="text-green-400 font-bold text-lg">Our Ironclad Guarantee</span>
              </div>
              <p className="text-gray-300">
                We will schedule <span className="text-red-400 font-bold">5 qualified appointments</span> in your first 30 days,
                or you receive a <span className="text-green-400 font-bold">100% refund</span> of your investment.
                You have nothing to lose.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FinalCTA;
