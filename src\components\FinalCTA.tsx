
import React from 'react';
import { Button } from '@/components/ui/button';
import { Clock } from 'lucide-react';

const FinalCTA = () => {
  return (
    <section id="final-cta" className="py-16 md:py-20 lg:py-24 bg-gradient-to-t from-red-900/20 via-black to-black">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-4xl mx-auto text-center space-y-8 md:space-y-12">
          <div className="space-y-4 md:space-y-6">
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-white leading-tight">
              Ready to Stop Relying on <span className="text-red-600">Luck</span> for Leads?
            </h2>
            <p className="text-lg md:text-xl lg:text-2xl text-gray-300 max-w-4xl mx-auto font-semibold leading-relaxed">
              Roofers Growth Engine gives you the team, technology, and training to consistently win new jobs every month. It's like having a veteran sales rep and a marketing department, for a fraction of the cost.
            </p>
            <p className="text-base md:text-lg text-gray-400 max-w-3xl mx-auto">
              Don't let another high-value lead slip away or another month of ad budget go up in smoke – this system will make sure every lead is followed-up and maximized into revenue.
            </p>
          </div>

          <div className="bg-gradient-to-r from-red-600/20 via-red-500/15 to-red-600/20 border-2 border-red-600/40 rounded-2xl p-8 mb-10 attention-grabber card-hover">
            <div className="flex items-center justify-center space-x-4 mb-5">
              <Clock className="w-8 h-8 text-red-400 animate-pulse" />
              <span className="text-red-300 font-black text-2xl">⚠️ Limited Enrollment</span>
            </div>
            <p className="text-gray-200 text-xl font-semibold text-center leading-relaxed">
              We only onboard <span className="text-red-400 font-black text-2xl bg-red-600/20 px-3 py-1 rounded-lg">5 new roofing companies per month</span> to ensure quality service. <span className="text-white font-bold">Our next slots are nearly full.</span>
            </p>
          </div>

          <div className="space-y-8">
            <Button
              size="lg"
              className="w-full cta-primary cta-pulse text-white px-12 py-10 text-2xl lg:text-3xl font-black rounded-2xl attention-grabber group"
            >
              <span className="flex items-center justify-center gap-4">
                <span className="text-3xl lg:text-4xl">📅</span>
                <span>Book My Free Strategy Call Now</span>
                <span className="text-2xl lg:text-3xl animate-bounce group-hover:animate-pulse">🚀</span>
              </span>
            </Button>

            <div className="bg-gradient-to-r from-green-600/20 via-green-500/15 to-green-600/20 border border-green-600/30 rounded-xl p-6">
              <div className="flex items-center justify-center space-x-2 mb-3">
                <span className="text-2xl">🛡️</span>
                <span className="text-green-300 font-bold text-xl">Our Ironclad Guarantee</span>
              </div>
              <p className="text-gray-200 text-lg text-center">
                We will schedule <span className="text-red-400 font-bold">5 qualified appointments</span> in your first 30 days,
                or you receive a <span className="text-green-300 font-bold">100% refund</span> of your investment.
                You have nothing to lose.
              </p>
            </div>

            <div className="bg-gradient-to-r from-blue-600/20 via-blue-500/15 to-blue-600/20 border border-blue-600/30 rounded-xl p-6">
              <p className="text-blue-200 text-center text-lg font-semibold">
                <span className="text-blue-300 font-bold">What happens next:</span> After you book, you'll pick a convenient time for a Zoom call and our growth specialist will audit your current lead flow and show you how the AI can start generating appointments within days.
              </p>
            </div>
          </div>

          <div className="mt-8 p-6 bg-gray-900/50 rounded-xl border border-gray-700">
            <p className="text-gray-400 text-center text-sm">
              <span className="text-gray-300 font-semibold">P.S.</span> In case you skimmed to the bottom: Roofers Growth Engine is a done-for-you AI sales assistant + pro ad service that will consistently fill your calendar with qualified appointments. We're only taking a few new clients this quarter – book a free call to see if you qualify.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FinalCTA;
