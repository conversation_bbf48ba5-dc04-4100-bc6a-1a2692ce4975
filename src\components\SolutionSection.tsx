
import React from 'react';
import { Bo<PERSON>, Calendar, Phone, MessageSquare } from 'lucide-react';

const SolutionSection = () => {
  const features = [
    {
      icon: Phone,
      title: "Never Lose a Hot Lead to Slow Follow-Up",
      description: "AI assistant calls every inquiry within 120 seconds, so no prospect falls through the cracks while you're busy on a job site."
    },
    {
      icon: Calendar,
      title: "Appointments Appear on Your Calendar Automatically",
      description: "Spend time closing deals, not chasing leads. The AI qualifies prospects and books only serious buyers directly to your schedule."
    },
    {
      icon: MessageSquare,
      title: "Consistent Lead Generation Every Week",
      description: "Professional ad campaigns drive steady inquiries from homeowners actively looking for roofing services in your area."
    },
    {
      icon: Bot,
      title: "Gain Insider Advertising Skills for Long-Term Growth",
      description: "Learn from ex-Facebook specialists how to run winning campaigns, so you can fuel growth independently after 3 months."
    }
  ];

  return (
    <section id="solution" className="py-20 md:py-32 lg:py-40 bg-gradient-to-b from-black via-gray-900/30 to-black relative overflow-hidden">
      {/* Enhanced background effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-[800px] h-[800px] bg-red-600/12 rounded-full blur-[150px]"></div>
        <div className="absolute bottom-1/4 right-1/4 w-[700px] h-[700px] bg-red-600/8 rounded-full blur-[130px]"></div>
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.015)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.015)_1px,transparent_1px)] bg-[size:140px_140px]"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="max-w-8xl mx-auto">
          <div className="text-center space-y-8 md:space-y-12 mb-20 md:mb-24 lg:mb-32">
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl 2xl:text-9xl font-black text-white leading-[0.85] tracking-tighter">
              Meet the <span className="text-transparent bg-gradient-to-r from-red-500 via-red-400 to-red-600 bg-clip-text">Roofers Growth Engine</span>
            </h2>
            <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl text-gray-300 max-w-5xl mx-auto font-bold">
              Your AI-Powered Sales Partner
            </p>
            <div className="max-w-4xl mx-auto space-y-6 md:space-y-8">
              <p className="text-lg sm:text-xl md:text-2xl text-gray-200 font-semibold leading-relaxed">
                Roofers Growth Engine is an all-in-one AI sales infrastructure that automatically follows up with your leads and fills your calendar, while expert marketers bring you a steady flow of new prospects. Finally, a growth system built for roofing contractors.
              </p>
              <p className="text-base sm:text-lg md:text-xl text-gray-300 leading-relaxed">
                It combines an AI voice appointment setter that calls and texts your leads within minutes, with done-for-you Facebook ad campaigns run by former Facebook ads insiders. No more wasted leads or ad budget – the system turns inquiries into booked jobs for you.
              </p>
              <div className="bg-gradient-to-r from-blue-600/20 via-blue-500/15 to-blue-600/20 border border-blue-600/30 rounded-2xl p-6 md:p-8 backdrop-blur-sm">
                <p className="text-blue-200 font-bold text-lg md:text-xl text-center">
                  <span className="text-blue-300">🔑 Key Differentiator:</span> Unlike typical marketing services, this is a done-with-you package: we build your AI assistant, run your ads with insider expertise, train you or your team to manage it, and then hand you the keys – you keep all the tech and systems. No ongoing contracts or dependencies.
                </p>
              </div>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 md:gap-16 lg:gap-20 items-center mb-16 md:mb-20 lg:mb-24">
            <div className="space-y-8 md:space-y-12 lg:space-y-16">
              {features.map((feature, index) => (
                <div key={index} className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-6 md:space-x-8 group hover:scale-105 transition-all duration-700 p-6 md:p-8 rounded-2xl md:rounded-3xl hover:bg-gray-900/40">
                  <div className="flex-shrink-0 w-16 h-16 md:w-18 md:h-18 lg:w-20 lg:h-20 bg-gradient-to-r from-red-600 to-red-700 rounded-2xl md:rounded-3xl flex items-center justify-center group-hover:from-red-500 group-hover:to-red-600 transition-all duration-700 shadow-2xl shadow-red-600/50 group-hover:shadow-red-600/70">
                    <feature.icon className="w-8 h-8 md:w-9 md:h-9 lg:w-10 lg:h-10 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl md:text-2xl lg:text-3xl font-black text-white mb-3 md:mb-4 group-hover:text-red-300 transition-colors duration-500">{feature.title}</h3>
                    <p className="text-gray-300 text-base md:text-lg lg:text-xl leading-relaxed font-medium">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="relative mt-12 lg:mt-0">
              <div className="bg-gradient-to-br from-gray-900/95 via-gray-800/80 to-gray-900/95 backdrop-blur-3xl rounded-[2rem] md:rounded-[3rem] p-8 md:p-12 lg:p-16 border border-gray-600/40 shadow-[0_50px_100px_-20px_rgba(0,0,0,0.9)] hover:border-red-600/40 transition-all duration-700">
                <div className="space-y-8 md:space-y-10">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0">
                    <span className="text-gray-300 text-lg md:text-xl lg:text-2xl font-bold">Lead Response Time</span>
                    <span className="text-green-400 font-black text-2xl md:text-3xl">47 seconds</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-3 md:h-4">
                    <div className="bg-gradient-to-r from-red-600 via-yellow-500 to-green-500 h-3 md:h-4 rounded-full w-full animate-pulse shadow-lg"></div>
                  </div>

                  <div className="space-y-6 md:space-y-8 mt-12 md:mt-16">
                    <div className="bg-gradient-to-r from-gray-800/90 to-gray-900/90 rounded-2xl md:rounded-3xl p-6 md:p-8 border-2 border-green-600/40 hover:border-green-600/60 transition-all duration-500 shadow-lg shadow-green-600/20">
                      <div className="text-green-300 text-base md:text-lg font-black mb-2 md:mb-3">✅ Lead Captured</div>
                      <div className="text-white text-lg md:text-xl lg:text-2xl font-bold">John Smith - Roof Repair - Phoenix</div>
                      <div className="text-gray-300 text-base md:text-lg mt-2">Priority: High-value prospect</div>
                    </div>
                    <div className="bg-gradient-to-r from-gray-800/90 to-gray-900/90 rounded-2xl md:rounded-3xl p-6 md:p-8 border-2 border-blue-600/40 hover:border-blue-600/60 transition-all duration-500 shadow-lg shadow-blue-600/20">
                      <div className="text-blue-300 text-base md:text-lg font-black mb-2 md:mb-3">📞 AI Calling</div>
                      <div className="text-white text-lg md:text-xl lg:text-2xl font-bold">"Hi John, I'm calling about your roof repair..."</div>
                      <div className="text-gray-300 text-base md:text-lg mt-2">Qualification in progress</div>
                    </div>
                    <div className="bg-gradient-to-r from-gray-800/90 to-gray-900/90 rounded-2xl md:rounded-3xl p-6 md:p-8 border-2 border-red-600/40 hover:border-red-600/60 transition-all duration-500 shadow-lg shadow-red-600/20">
                      <div className="text-red-300 text-base md:text-lg font-black mb-2 md:mb-3">📅 Appointment Booked</div>
                      <div className="text-white text-lg md:text-xl lg:text-2xl font-bold">Tomorrow 2:30 PM - $8,500 project</div>
                      <div className="text-gray-300 text-base md:text-lg mt-2">Ready for your inspection</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-red-600/15 to-blue-600/15 rounded-[3rem] blur-3xl -z-10"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SolutionSection;
