
import React from 'react';

const HowItWorks = () => {
  const steps = [
    {
      number: "1",
      title: "We Launch Your Ads",
      description: "Our expert team launches targeted Facebook ads or responds to your incoming leads using proven roofing industry strategies."
    },
    {
      number: "2",
      title: "AI Agent Calls & Qualifies",
      description: "Our AI assistant calls every lead within 120 seconds, qualifies them using our proven script, and books qualified prospects."
    },
    {
      number: "3",
      title: "You Show Up & Close",
      description: "You arrive at pre-qualified appointments with homeowners ready to buy. No more wasted time on tire-kickers."
    }
  ];

  return (
    <section id="how-it-works" className="py-20 md:py-32 lg:py-40 bg-gradient-to-b from-black via-gray-900/30 to-gray-900">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center space-y-6 md:space-y-8 mb-16 md:mb-20 lg:mb-24">
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-black text-white leading-tight tracking-tight">
              3 Steps to Booking More Jobs <span className="text-transparent bg-gradient-to-r from-red-500 via-red-400 to-red-600 bg-clip-text">Automatically</span>
            </h2>
            <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl text-gray-400 font-medium max-w-4xl mx-auto">
              It's simple. We handle the heavy lifting, you focus on closing deals.
            </p>
          </div>

          <div className="space-y-8 md:space-y-12">
            {steps.map((step, index) => (
              <div key={index} className="relative group">
                <div className="flex flex-col sm:flex-row items-start space-y-6 sm:space-y-0 sm:space-x-8 md:space-x-10">
                  <div className="flex-shrink-0 w-20 h-20 md:w-22 md:h-22 lg:w-24 lg:h-24 bg-gradient-to-r from-red-600 to-red-700 rounded-full flex items-center justify-center text-white text-2xl md:text-3xl lg:text-4xl font-black shadow-2xl shadow-red-600/40 group-hover:scale-110 transition-transform duration-500">
                    {step.number}
                  </div>
                  <div className="flex-1 bg-gradient-to-br from-gray-900/90 via-gray-800/70 to-gray-900/90 backdrop-blur-2xl rounded-2xl md:rounded-3xl p-8 md:p-10 lg:p-12 border border-gray-700/50 hover:border-red-600/30 transition-all duration-500 shadow-[0_25px_50px_-12px_rgba(0,0,0,0.8)] group-hover:shadow-[0_35px_60px_-12px_rgba(220,38,38,0.2)]">
                    <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-white mb-4 md:mb-6 group-hover:text-red-400 transition-colors duration-300">{step.title}</h3>
                    <p className="text-gray-400 text-base md:text-lg lg:text-xl leading-relaxed">{step.description}</p>
                  </div>
                </div>

                {index < steps.length - 1 && (
                  <div className="absolute left-10 md:left-11 lg:left-12 top-20 md:top-22 lg:top-24 w-1 h-8 md:h-10 lg:h-12 bg-gradient-to-b from-red-600/80 to-red-600/20 hidden sm:block"></div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
