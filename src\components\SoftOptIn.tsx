
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Download, CheckCircle } from 'lucide-react';

const SoftOptIn = () => {
  const benefits = [
    "Exact AI caller script we use",
    "5-step rapid response framework", 
    "Qualification questions that convert",
    "Objection handling templates"
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-black via-gray-900/30 to-black relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-blue-600/8 rounded-full blur-[120px]"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-br from-gray-900/95 via-gray-800/80 to-gray-900/95 backdrop-blur-3xl rounded-[3rem] p-16 border border-gray-600/40 shadow-[0_50px_100px_-20px_rgba(0,0,0,0.9)] hover:border-blue-600/40 transition-all duration-700">
            <div className="space-y-10">
              <div>
                <h2 className="text-5xl lg:text-6xl font-black text-white mb-6 leading-tight">
                  Download Our <span className="text-transparent bg-gradient-to-r from-blue-500 via-blue-400 to-blue-600 bg-clip-text">5-Step Lead Response SOP</span>
                </h2>
                <p className="text-2xl text-gray-300 font-medium">
                  The exact system that books 15+ appointments per month for our clients
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-6 text-left">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-2xl">
                    <CheckCircle className="w-6 h-6 text-blue-400 flex-shrink-0" />
                    <span className="text-white text-lg font-medium">{benefit}</span>
                  </div>
                ))}
              </div>

              <div className="space-y-6">
                <Button 
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 hover:from-blue-700 hover:via-blue-600 hover:to-blue-700 text-white px-12 py-6 text-xl font-black rounded-3xl transition-all duration-500 shadow-2xl shadow-blue-600/40 hover:shadow-blue-600/60 hover:scale-105 border-0"
                >
                  <Download className="w-6 h-6 mr-3" />
                  Get Free SOP (Instant Download)
                </Button>
                
                <p className="text-gray-400 text-sm">
                  No spam. Just the proven system that's booked over $2.3M in pipeline.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SoftOptIn;
