
import React, { useState, useEffect } from 'react';
import { Play, Phone, Calendar, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

const InteractiveDemo = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  const demoSteps = [
    {
      icon: Phone,
      title: "Lead Comes In",
      description: "New roofing lead from Phoenix - Storm damage repair",
      status: "⚡ Live Lead Alert"
    },
    {
      icon: Phone,
      title: "AI Calls Instantly",
      description: "AI dials prospect in 47 seconds",
      status: "📞 Calling now..."
    },
    {
      icon: Calendar,
      title: "Appointment Booked",
      description: "Tomorrow 2:30 PM - $12,500 project qualified",
      status: "✅ Confirmed"
    }
  ];

  useEffect(() => {
    if (isPlaying) {
      const interval = setInterval(() => {
        setCurrentStep((prev) => {
          if (prev >= demoSteps.length - 1) {
            setIsPlaying(false);
            return 0;
          }
          return prev + 1;
        });
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [isPlaying, demoSteps.length]);

  const startDemo = () => {
    setCurrentStep(0);
    setIsPlaying(true);
  };

  return (
    <section className="py-16 md:py-24 lg:py-32 bg-gradient-to-b from-gray-900/50 to-black relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-[600px] h-[600px] bg-red-600/10 rounded-full blur-[100px]"></div>
        <div className="absolute bottom-1/4 right-1/4 w-[500px] h-[500px] bg-red-600/8 rounded-full blur-[80px]"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="max-w-6xl mx-auto text-center">
          <div className="mb-12 md:mb-16">
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-black text-white mb-6 md:mb-8 leading-tight tracking-tight">
              See How <span className="text-transparent bg-gradient-to-r from-red-500 via-red-400 to-red-600 bg-clip-text">AI Books Jobs</span> in 30 Seconds
            </h2>
            <p className="text-lg md:text-xl lg:text-2xl text-gray-300 mb-8 md:mb-12 font-medium max-w-4xl mx-auto">
              Watch our AI turn a cold lead into a booked appointment automatically
            </p>
          </div>

          <div className="bg-gradient-to-br from-gray-900/95 via-gray-800/80 to-gray-900/95 backdrop-blur-3xl rounded-[2rem] md:rounded-[3rem] p-8 md:p-12 lg:p-16 border border-gray-600/40 shadow-[0_50px_100px_-20px_rgba(0,0,0,0.9)] mb-8 md:mb-12">
            {!isPlaying ? (
              <div className="text-center">
                <Button
                  onClick={startDemo}
                  size="lg"
                  className="w-full sm:w-auto bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-700 hover:via-red-600 hover:to-red-700 text-white px-8 md:px-12 lg:px-16 py-6 md:py-8 text-lg md:text-xl lg:text-2xl font-black rounded-2xl md:rounded-3xl transition-all duration-500 shadow-2xl shadow-red-600/40 hover:shadow-red-600/60 hover:scale-105 border-0 group relative overflow-hidden"
                >
                  <Play className="w-6 h-6 md:w-8 md:h-8 mr-3 md:mr-4" />
                  <span className="relative z-10">Start Interactive Demo</span>
                </Button>
                <p className="text-gray-400 mt-4 md:mt-6 text-base md:text-lg">Click to see the magic happen</p>
              </div>
            ) : (
              <div className="space-y-6 md:space-y-8 lg:space-y-12">
                {demoSteps.map((step, index) => {
                  const StepIcon = step.icon;
                  const isActive = index === currentStep;
                  const isCompleted = index < currentStep;

                  return (
                    <div
                      key={index}
                      className={`flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6 md:space-x-8 p-6 md:p-8 rounded-2xl md:rounded-3xl transition-all duration-1000 ${
                        isActive ? 'bg-red-600/20 border-2 border-red-600/50 scale-105' :
                        isCompleted ? 'bg-green-600/20 border-2 border-green-600/50' :
                        'bg-gray-800/50 border border-gray-600/30'
                      }`}
                    >
                      <div className={`w-16 h-16 md:w-20 md:h-20 rounded-full flex items-center justify-center transition-all duration-500 flex-shrink-0 ${
                        isActive ? 'bg-red-600 animate-pulse' :
                        isCompleted ? 'bg-green-600' :
                        'bg-gray-700'
                      }`}>
                        {isCompleted ? (
                          <CheckCircle className="w-8 h-8 md:w-10 md:h-10 text-white" />
                        ) : (
                          <StepIcon className="w-8 h-8 md:w-10 md:h-10 text-white" />
                        )}
                      </div>
                      <div className="flex-1 text-left">
                        <div className={`text-base md:text-lg font-bold mb-2 ${
                          isActive ? 'text-red-300' :
                          isCompleted ? 'text-green-300' :
                          'text-gray-400'
                        }`}>
                          {step.status}
                        </div>
                        <h3 className="text-xl md:text-2xl lg:text-3xl font-black text-white mb-2">{step.title}</h3>
                        <p className="text-gray-300 text-base md:text-lg lg:text-xl">{step.description}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          <div className="grid md:grid-cols-3 gap-6 md:gap-8 text-center">
            <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 rounded-xl md:rounded-2xl p-6 md:p-8 border border-gray-600/40">
              <div className="text-3xl md:text-4xl font-black text-red-400 mb-3 md:mb-4">47 Sec</div>
              <div className="text-white text-lg md:text-xl font-bold">Average Response Time</div>
            </div>
            <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 rounded-xl md:rounded-2xl p-6 md:p-8 border border-gray-600/40">
              <div className="text-3xl md:text-4xl font-black text-green-400 mb-3 md:mb-4">92%</div>
              <div className="text-white text-lg md:text-xl font-bold">Conversion Rate</div>
            </div>
            <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 rounded-xl md:rounded-2xl p-6 md:p-8 border border-gray-600/40">
              <div className="text-3xl md:text-4xl font-black text-blue-400 mb-3 md:mb-4">24/7</div>
              <div className="text-white text-lg md:text-xl font-bold">Never Miss a Lead</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default InteractiveDemo;
