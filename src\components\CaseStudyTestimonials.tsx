
import React from 'react';
import { TrendingUp, Calendar, DollarSign } from 'lucide-react';

const CaseStudyTestimonials = () => {
  const caseStudies = [
    {
      name: "<PERSON>",
      company: "Storm Shield Roofing",
      location: "Houston, TX",
      quote: "We had no CRM, no ad strategy, and were calling leads back hours later. With the Roofers Growth Engine, we got 22 booked appointments in 4 weeks and closed $87K in new business. My wife finally made me take a weekend off because the AI was handling everything.",
      pipeline: "$87K",
      closeRate: "31%",
      timeframe: "First 30 days",
      metric: "22 Appointments",
      image: "🏠"
    },
    {
      name: "<PERSON>",
      company: "Elite Roof Solutions",
      location: "Denver, CO",
      quote: "I was spending 4 hours a day playing phone tag with leads. Now the AI handles everything and I show up to pre-qualified appointments. It's like having a sales team that never sleeps. We've 5x'd our appointment volume and I actually have time to focus on the business.",
      pipeline: "$95K",
      closeRate: "28%",
      timeframe: "Second month",
      metric: "5x Appointment Volume",
      image: "⚡"
    },
    {
      name: "<PERSON>",
      company: "Phoenix Roofing Pros",
      location: "Phoenix, AZ",
      quote: "The AI sounds more professional than my actual receptionist. Prospects don't even realize they're talking to a bot. We went from 3 appointments per week to 15+ consistently. In beta tests, we saw $120K in new projects in 90 days.",
      pipeline: "$120K",
      closeRate: "35%",
      timeframe: "90 days",
      metric: "400% Growth",
      image: "🚀"
    }
  ];

  return (
    <section className="py-20 md:py-32 lg:py-40 bg-gradient-to-b from-gray-900/50 via-black to-gray-900/30 relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-1/3 left-1/3 w-[800px] h-[800px] bg-red-600/15 rounded-full blur-[150px]"></div>
        <div className="absolute bottom-1/3 right-1/3 w-[700px] h-[700px] bg-red-600/10 rounded-full blur-[130px]"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="max-w-8xl mx-auto">
          <div className="text-center space-y-8 md:space-y-12 mb-20 md:mb-24 lg:mb-32">
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl 2xl:text-9xl font-black text-white leading-[0.85] tracking-tighter">
              Real Stories. <span className="text-transparent bg-gradient-to-r from-red-500 via-red-400 to-red-600 bg-clip-text">Real Results.</span>
            </h2>
            <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl text-gray-300 font-bold">
              See how contractors transformed their businesses
            </p>
          </div>

          <div className="space-y-12 md:space-y-16 lg:space-y-20">
            {caseStudies.map((study, index) => (
              <div key={index} className="group hover:scale-[1.02] transition-all duration-700">
                <div className="bg-gradient-to-br from-gray-900/95 via-gray-800/80 to-gray-900/95 backdrop-blur-3xl rounded-[2rem] md:rounded-[3rem] p-8 md:p-12 lg:p-16 xl:p-20 border border-gray-600/40 hover:border-red-600/50 transition-all duration-700 shadow-[0_50px_100px_-20px_rgba(0,0,0,0.9)] group-hover:shadow-[0_60px_120px_-20px_rgba(220,38,38,0.3)]">

                  <div className="grid lg:grid-cols-3 gap-8 md:gap-12 lg:gap-16 items-center">
                    {/* Quote Section */}
                    <div className="lg:col-span-2 space-y-6 md:space-y-8">
                      <div className="text-6xl md:text-7xl lg:text-8xl">{study.image}</div>
                      <blockquote className="text-gray-200 text-lg sm:text-xl md:text-2xl lg:text-3xl mb-6 md:mb-8 italic leading-relaxed font-medium">
                        "{study.quote}"
                      </blockquote>

                      <div className="border-t border-gray-600/50 pt-6 md:pt-8">
                        <div className="font-black text-white text-2xl md:text-3xl lg:text-4xl">{study.name}</div>
                        <div className="text-gray-300 text-lg md:text-xl lg:text-2xl mt-2 font-semibold">{study.company}</div>
                        <div className="text-gray-400 mt-1 text-base md:text-lg lg:text-xl">{study.location}</div>
                      </div>
                    </div>

                    {/* Metrics Section */}
                    <div className="space-y-6 md:space-y-8 mt-8 lg:mt-0">
                      <div className="bg-gradient-to-br from-red-600/20 to-red-700/20 border-2 border-red-600/40 rounded-2xl md:rounded-3xl p-6 md:p-8 text-center hover:border-red-600/60 transition-all duration-500">
                        <DollarSign className="w-10 h-10 md:w-12 md:h-12 text-red-400 mx-auto mb-3 md:mb-4" />
                        <div className="text-2xl md:text-3xl lg:text-4xl font-black text-white mb-2">{study.pipeline}</div>
                        <div className="text-red-300 text-base md:text-lg font-semibold">Pipeline Generated</div>
                      </div>

                      <div className="bg-gradient-to-br from-green-600/20 to-green-700/20 border-2 border-green-600/40 rounded-2xl md:rounded-3xl p-6 md:p-8 text-center hover:border-green-600/60 transition-all duration-500">
                        <TrendingUp className="w-10 h-10 md:w-12 md:h-12 text-green-400 mx-auto mb-3 md:mb-4" />
                        <div className="text-2xl md:text-3xl lg:text-4xl font-black text-white mb-2">{study.closeRate}</div>
                        <div className="text-green-300 text-base md:text-lg font-semibold">Close Rate</div>
                      </div>

                      <div className="bg-gradient-to-br from-blue-600/20 to-blue-700/20 border-2 border-blue-600/40 rounded-2xl md:rounded-3xl p-6 md:p-8 text-center hover:border-blue-600/60 transition-all duration-500">
                        <Calendar className="w-10 h-10 md:w-12 md:h-12 text-blue-400 mx-auto mb-3 md:mb-4" />
                        <div className="text-2xl md:text-3xl lg:text-4xl font-black text-white mb-2">{study.metric}</div>
                        <div className="text-blue-300 text-base md:text-lg font-semibold">{study.timeframe}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CaseStudyTestimonials;
