
import React from 'react';

const CredibilityBar = () => {
  return (
    <section className="bg-gray-900 py-8 md:py-12 border-y border-gray-800">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-6xl mx-auto text-center space-y-6 md:space-y-8">
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="flex text-yellow-400">
              {[...Array(5)].map((_, i) => (
                <span key={i} className="text-xl md:text-2xl">★</span>
              ))}
            </div>
            <span className="text-white text-base md:text-lg font-medium">4.9/5 Average Rating</span>
          </div>

          <p className="text-gray-400 text-base md:text-lg">
            Trusted by <span className="text-red-400 font-bold">500+ Contractors</span> Nationwide
          </p>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 items-center opacity-60">
            <div className="bg-gray-800 rounded-lg p-3 md:p-4 text-center">
              <div className="text-white font-bold text-sm md:text-base">Phoenix Roofing</div>
            </div>
            <div className="bg-gray-800 rounded-lg p-3 md:p-4 text-center">
              <div className="text-white font-bold text-sm md:text-base">Texas Storm Pro</div>
            </div>
            <div className="bg-gray-800 rounded-lg p-3 md:p-4 text-center">
              <div className="text-white font-bold text-sm md:text-base">Elite Contractors</div>
            </div>
            <div className="bg-gray-800 rounded-lg p-3 md:p-4 text-center">
              <div className="text-white font-bold text-sm md:text-base">Summit Roofing</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CredibilityBar;
