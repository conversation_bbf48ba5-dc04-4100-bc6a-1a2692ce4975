
import React from 'react';
import { X } from 'lucide-react';

const PainSection = () => {
  const painPoints = [
    {
      title: "Unpredictable Lead Flow is Crippling Your Business",
      description: "One month you're overbooked, the next you're scrambling with an empty pipeline. You can't plan, can't scale, can't sleep."
    },
    {
      title: "Wasted Ad Spend and Slow Follow-ups Cost You Jobs",
      description: "Every missed call or forgotten follow-up is money left on the table. While you're playing phone tag, competitors are booking the profitable jobs."
    },
    {
      title: "Working 12-Hour Days Chasing New Business is Burning You Out",
      description: "You became a roofer to build something, not to be a full-time telemarketer. But without leads, there's no business."
    },
    {
      title: "HomeAdvisor and Lead Services Are Bleeding You Dry",
      description: "Paying $200+ per lead just to compete with 5 other contractors for tire-kickers who aren't ready to buy."
    },
    {
      title: "Your Best Prospects Are Calling Competitors Back First",
      description: "By the time you call that hot lead back, they've already scheduled with someone who responded in minutes, not hours."
    }
  ];

  return (
    <section className="py-20 md:py-32 lg:py-40 bg-gradient-to-b from-gray-900/30 via-black to-black relative overflow-hidden">
      {/* Enhanced background gradient effects */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-red-600/8 to-transparent"></div>
        <div className="absolute top-1/3 left-1/4 w-[700px] h-[700px] bg-red-600/15 rounded-full blur-[150px]"></div>
        <div className="absolute bottom-1/3 right-1/4 w-[600px] h-[600px] bg-red-600/10 rounded-full blur-[130px]"></div>
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.015)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.015)_1px,transparent_1px)] bg-[size:120px_120px]"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="max-w-7xl mx-auto text-center space-y-16 md:space-y-20 lg:space-y-24">
          <div className="space-y-8 md:space-y-12">
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl 2xl:text-9xl font-black text-white leading-[0.85] tracking-tighter">
              Unpredictable Lead Flow is <span className="text-transparent bg-gradient-to-r from-red-500 via-red-400 to-red-600 bg-clip-text">Crippling</span> Your Roofing Business
            </h2>
            <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl text-gray-300 max-w-5xl mx-auto font-bold">Sound familiar? You're not alone...</p>
          </div>

          <div className="bg-gradient-to-br from-gray-900/95 via-gray-800/80 to-gray-900/95 backdrop-blur-3xl rounded-[2rem] md:rounded-[3rem] p-8 md:p-12 lg:p-16 xl:p-20 border border-gray-600/40 shadow-[0_50px_100px_-20px_rgba(0,0,0,0.9)] hover:border-red-600/40 transition-all duration-700">
            <div className="space-y-8 md:space-y-10 lg:space-y-12">
              {painPoints.map((point, index) => (
                <div key={index} className="text-left space-y-4 md:space-y-6 group card-hover p-8 md:p-10 rounded-2xl md:rounded-3xl bg-gradient-to-r from-gray-900/50 to-gray-800/50 border border-gray-700/50 hover:border-red-600/30 hover:bg-red-600/5">
                  <div className="flex items-start space-x-6 md:space-x-8">
                    <div className="flex-shrink-0 w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-red-600 to-red-700 rounded-full flex items-center justify-center group-hover:from-red-500 group-hover:to-red-600 transition-all duration-700 shadow-xl shadow-red-600/50 group-hover:shadow-red-500/70 mt-1">
                      <X className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 text-white font-bold" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl text-white font-black leading-tight group-hover:text-red-200 transition-all duration-700 mb-4 md:mb-5">
                        {point.title}
                      </h3>
                      <p className="text-lg sm:text-xl md:text-2xl text-gray-300 font-semibold leading-relaxed group-hover:text-gray-200 transition-all duration-700">
                        {point.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-gradient-to-r from-red-600/30 via-red-500/20 to-red-700/30 border-2 border-red-600/50 rounded-[1.5rem] md:rounded-[2.5rem] p-8 md:p-12 lg:p-16 backdrop-blur-sm shadow-2xl shadow-red-600/30">
            <p className="text-red-200 font-black text-xl sm:text-2xl md:text-3xl lg:text-4xl leading-relaxed text-center">
              The painful truth: While you're playing phone tag and chasing dead leads, your competitors with better systems are booking the profitable jobs. <span className="text-white">Every day you wait is money left on the table.</span>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PainSection;
