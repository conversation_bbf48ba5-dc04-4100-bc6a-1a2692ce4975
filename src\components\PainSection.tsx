
import React from 'react';
import { X } from 'lucide-react';

const PainSection = () => {
  const painPoints = [
    "Manual Follow-up (Leads go cold while you're busy)",
    "Missed Calls (Your best prospects call competitors)",
    "Unqualified Appointments (Waste time on tire-kickers)",
    "Expensive Dead-End Leads (HomeAdvisor nightmare)",
    "Inconsistent Lead Flow (Feast or famine cycles)"
  ];

  return (
    <section className="py-20 md:py-32 lg:py-40 bg-gradient-to-b from-gray-900/30 via-black to-black relative overflow-hidden">
      {/* Enhanced background gradient effects */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-red-600/8 to-transparent"></div>
        <div className="absolute top-1/3 left-1/4 w-[700px] h-[700px] bg-red-600/15 rounded-full blur-[150px]"></div>
        <div className="absolute bottom-1/3 right-1/4 w-[600px] h-[600px] bg-red-600/10 rounded-full blur-[130px]"></div>
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.015)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.015)_1px,transparent_1px)] bg-[size:120px_120px]"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="max-w-7xl mx-auto text-center space-y-16 md:space-y-20 lg:space-y-24">
          <div className="space-y-8 md:space-y-12">
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl 2xl:text-9xl font-black text-white leading-[0.85] tracking-tighter">
              Most Contractors Lose <span className="text-transparent bg-gradient-to-r from-red-500 via-red-400 to-red-600 bg-clip-text">40% of Leads</span>
            </h2>
            <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl text-gray-300 max-w-5xl mx-auto font-bold">Here's Why Your Business Growth Has Stalled...</p>
          </div>

          <div className="bg-gradient-to-br from-gray-900/95 via-gray-800/80 to-gray-900/95 backdrop-blur-3xl rounded-[2rem] md:rounded-[3rem] p-8 md:p-12 lg:p-16 xl:p-20 border border-gray-600/40 shadow-[0_50px_100px_-20px_rgba(0,0,0,0.9)] hover:border-red-600/40 transition-all duration-700">
            <div className="space-y-8 md:space-y-10 lg:space-y-12">
              {painPoints.map((point, index) => (
                <div key={index} className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-6 md:space-x-8 lg:space-x-10 group hover:scale-105 transition-all duration-700 p-4 md:p-6 rounded-2xl md:rounded-3xl hover:bg-red-600/10">
                  <div className="flex-shrink-0 w-16 h-16 md:w-18 md:h-18 lg:w-20 lg:h-20 bg-gradient-to-r from-red-600 to-red-700 rounded-xl md:rounded-[2rem] flex items-center justify-center shadow-2xl shadow-red-600/50 group-hover:shadow-red-600/70 transition-all duration-700 group-hover:scale-110">
                    <X className="w-8 h-8 md:w-9 md:h-9 lg:w-10 lg:h-10 text-white" />
                  </div>
                  <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl text-gray-200 group-hover:text-white transition-colors leading-relaxed pt-2 md:pt-4 lg:pt-6 font-bold">
                    {point}
                  </p>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-gradient-to-r from-red-600/30 via-red-500/20 to-red-700/30 border-2 border-red-600/50 rounded-[1.5rem] md:rounded-[2.5rem] p-8 md:p-12 lg:p-16 backdrop-blur-sm shadow-2xl shadow-red-600/30">
            <p className="text-red-200 font-black text-xl sm:text-2xl md:text-3xl lg:text-4xl leading-relaxed">
              The painful truth: While you're playing phone tag and chasing dead leads,
              your competitors with better systems are booking the profitable jobs.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PainSection;
