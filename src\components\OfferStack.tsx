
import React from 'react';
import { Check } from 'lucide-react';

const OfferStack = () => {
  const offers = [
    {
      item: "AI Voice Appointment Setter",
      value: "One-Time Build",
      description: "Custom AI assistant that calls leads within 120 seconds"
    },
    {
      item: "3 Months Done-For-You Facebook Ads",
      value: "$9,800 Value",
      description: "Professionally managed campaigns by ex-Facebook specialists"
    },
    {
      item: "Ad Management Training & SOPs",
      value: "$2,500 Value",
      description: "Complete training to run your own winning campaigns"
    },
    {
      item: "Full Tech & System Ownership",
      value: "Priceless",
      description: "You own 100% of the technology and systems we build"
    },
    {
      item: "Proven Ad Copy Swipe Files",
      value: "$499 Bonus",
      description: "High-converting templates tested across hundreds of campaigns"
    },
    {
      item: "Email Follow-Up Templates",
      value: "$299 Bonus", 
      description: "Complete sequences that nurture leads into customers"
    }
  ];

  return (
    <section id="pricing" className="py-20 bg-black">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center space-y-6 mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white">
              Here's What You Get When You <span className="text-red-600">Book a Demo</span>
            </h2>
            <p className="text-xl text-gray-400">
              Everything you need to automate your roofing lead generation and booking
            </p>
          </div>
          
          <div className="bg-gradient-to-b from-gray-900 to-gray-800 rounded-2xl p-8 border border-gray-700">
            <div className="space-y-6">
              {offers.map((offer, index) => (
                <div key={index} className="flex items-start space-x-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700/50 hover:border-red-600/30 transition-all duration-300">
                  <div className="flex-shrink-0 w-8 h-8 bg-red-600 rounded-full flex items-center justify-center">
                    <Check className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="text-xl font-bold text-white">{offer.item}</h3>
                      <span className="text-red-400 font-bold text-lg">{offer.value}</span>
                    </div>
                    <p className="text-gray-400">{offer.description}</p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-8 p-6 bg-red-600/10 border border-red-600/30 rounded-xl">
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-2">Total Value: $13,097+</div>
                <div className="text-red-400 font-bold text-xl">Your Investment: A Fraction of That</div>
                <p className="text-gray-400 mt-2">Plus our 30-day guarantee - 5 appointments or full refund</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OfferStack;
