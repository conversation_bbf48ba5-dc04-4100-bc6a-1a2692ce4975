
import React from 'react';
import { Check } from 'lucide-react';

const OfferStack = () => {
  const offers = [
    {
      item: "AI Voice Appointment Setter (One-Time Build)",
      value: "Priceless",
      description: "An AI-powered virtual assistant that automatically calls new leads within 2 minutes and follows up multiple times until they book an appointment.",
      benefit: "Never lose a hot lead to slow follow-up – the AI works 24/7, so you don't have to play phone tag."
    },
    {
      item: "3 Months of Facebook Ads Management (by Former Facebook Insiders)",
      value: "$9,800 Value",
      description: "Three months of professionally managed Facebook advertising campaigns, run by our team of ex-Facebook ad specialists.",
      benefit: "High-quality lead flow without wasted spend – get in front of homeowners actively looking for roofing services."
    },
    {
      item: "Hands-On Ad Management Training",
      value: "$2,500 Value",
      description: "We'll train you or a team member over 3 months to run winning ads yourselves.",
      benefit: "You gain the skills and confidence to keep the leads coming even after our 3-month campaign, so you're not dependent on an agency."
    },
    {
      item: "Full Tech Ownership – No Lock-In",
      value: "Priceless",
      description: "All the AI tech and campaign frameworks are yours to keep.",
      benefit: "You retain full control of the system – if you ever part ways with us, your lead engine stays with you."
    },
    {
      item: "Bonus Resources (SOPs, Swipe Files, Courses)",
      value: "$1,299 Bonus",
      description: "You'll also get a package of bonuses: proven ad copy swipe files for roofing, step-by-step SOPs for lead follow-up, and mini-courses on Facebook marketing and sales – all tailored to roofing.",
      benefit: "Accelerate your success with plug-and-play scripts and education, so you're never starting from scratch."
    }
  ];

  return (
    <section id="pricing" className="py-20 bg-black">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center space-y-6 mb-16">
            <h2 className="text-4xl lg:text-6xl xl:text-7xl font-black text-white leading-tight">
              Here's What You Get with the <span className="text-transparent bg-gradient-to-r from-red-500 via-red-400 to-red-600 bg-clip-text">Roofers Growth Engine</span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-300 font-bold max-w-4xl mx-auto">
              Everything you need to automate your roofing lead generation and booking – yours to keep forever
            </p>
          </div>

          <div className="bg-gradient-to-b from-gray-900 to-gray-800 rounded-2xl p-8 border border-gray-700">
            <div className="space-y-6">
              {offers.map((offer, index) => (
                <div key={index} className="p-8 bg-gradient-to-br from-gray-900/70 to-gray-800/70 rounded-2xl border-2 border-gray-700/50 hover:border-red-600/40 card-hover space-y-6 group">
                  <div className="flex items-start space-x-6">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-red-600 to-red-700 rounded-full flex items-center justify-center mt-1 group-hover:from-red-500 group-hover:to-red-600 transition-all duration-300 shadow-lg shadow-red-600/50">
                      <Check className="w-6 h-6 text-white font-bold" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-4">
                        <h3 className="text-xl md:text-2xl font-black text-white pr-4 group-hover:text-red-100 transition-colors duration-300">{offer.item}</h3>
                        <span className="text-red-400 font-black text-lg md:text-xl whitespace-nowrap bg-red-600/10 px-3 py-1 rounded-full border border-red-600/20">{offer.value}</span>
                      </div>
                      <p className="text-gray-300 mb-4 leading-relaxed text-base md:text-lg font-medium group-hover:text-gray-200 transition-colors duration-300">{offer.description}</p>
                      <div className="bg-gradient-to-r from-green-600/15 to-green-500/15 border-2 border-green-600/30 rounded-xl p-4 attention-grabber">
                        <p className="text-green-300 font-bold text-sm md:text-base">
                          <span className="text-green-400 text-lg">✓</span> <span className="font-black">Benefit:</span> {offer.benefit}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-8 space-y-6">
              <div className="p-6 bg-gradient-to-r from-green-600/20 via-green-500/15 to-green-600/20 border border-green-600/30 rounded-xl">
                <div className="text-center space-y-3">
                  <div className="text-3xl font-black text-white">Total Value: $13,599+</div>
                  <div className="text-green-300 font-bold text-xl">Your Investment: A Fraction of That</div>
                  <p className="text-gray-200 text-lg">
                    Together, these components mean you'll have more appointments with qualified homeowners, less time wasted on dead leads, and ultimately more roofing jobs each month – without the marketing headaches.
                  </p>
                </div>
              </div>

              <div className="p-6 bg-gradient-to-r from-blue-600/20 via-blue-500/15 to-blue-600/20 border border-blue-600/30 rounded-xl">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 mb-3">
                    <span className="text-2xl">🛡️</span>
                    <span className="text-blue-300 font-bold text-xl">Our Ironclad Guarantee</span>
                  </div>
                  <p className="text-gray-200 text-lg">
                    5 qualified appointments in your first 30 days, or you receive a <span className="text-green-300 font-bold">100% refund</span> of your investment.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OfferStack;
